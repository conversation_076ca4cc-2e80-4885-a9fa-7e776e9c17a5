<template>
  <div class="route-tabs">
    <el-tabs
      v-model="activePath"
      type="card"
      closable
      @tab-click="handleClick"
      @tab-remove="handleClose"
    >
      <el-tab-pane
        v-for="tab in visitedViews"
        :key="tab.path"
        :label="tab.title"
        :name="tab.path"
      />
    </el-tabs>
  </div>
</template>

<script>
export default {
  computed: {
    visitedViews() {
      return this.$store.state.tabsView.visitedViews
    },
    activePath: {
      get() {
        return this.$route.path
      },
      set(val) {
        this.$router.push(val)
      }
    }
  },
  methods: {
    handleClick(tab) {
      this.$router.push(tab.name)
    },
    handleClose(path) {
      this.$store.dispatch('tabsView/removeView', path)
      if (this.$route.path === path) {
        const last = this.visitedViews[this.visitedViews.length - 1]
        this.$router.push(last?.path || '/')
      }
    }
  }
}
</script>

<style scoped>
.route-tabs {
  background: #fff;
  padding: 0 16px;
}
</style>