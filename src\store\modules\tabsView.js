const state = {
  visitedViews: [],
  cachedViews: []
}

const mutations = {
  ADD_VISITED_VIEW(state, view) {
    if (state.visitedViews.some(v => v.path === view.path)) return

    let title = view.meta.title || '未命名页面'

    if (view.name === 'EmailComposePage') {
      let mode = view.params?.mode
      if (!mode) {
        const segments = view.path.split('/')
        mode = segments[segments.length - 2] // 倒数第二段是 mode
      }

      const titleMap = {
        compose: '新写邮件',
        reply: '回复邮件',
        replyAll:'回复全部',
        forward: '转发邮件'
      }
      title = titleMap[mode] || '写邮件'
    }
    state.visitedViews.push({
      title,
      path: view.path,
      name: view.name
    })
    if (view.name && !state.cachedViews.includes(view.name)) {
      state.cachedViews.push(view.name)
    }
  },
  REMOVE_VISITED_VIEW(state, path) {
    const view = state.visitedViews.find(v => v.path === path)
    if (view) {
      state.cachedViews = state.cachedViews.filter(name => name !== view.name)
    }
    state.visitedViews = state.visitedViews.filter(v => v.path !== path)
  }
}

const actions = {
  addView({ commit }, view) {
    commit('ADD_VISITED_VIEW', view)
  },
  removeView({ commit }, path) {
    commit('REMOVE_VISITED_VIEW', path)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}