<template>
  <div class="email-detail-content">
    <!-- 邮件详情加载动画 -->
    <div v-if="detailLoading" class="email-loading-overlay">
      <div class="loading-spinner">
        <div class="spinner"></div>
        <div class="loading-text">加载中...</div>
      </div>
    </div>

    <!-- 邮件详情内容 -->
    <div v-if="emailDetail">
      <!-- 顶部导航栏 -->
      <div class="email-header">
        <!-- 使用新的邮件工具栏组件 -->
        <email-toolbar
          v-if="showToolbar"
          :email="emailDetail"
          :has-prev-email="hasPrevEmail"
          :has-next-email="hasNextEmail"
          @reply="handleReply"
          @reply-all="handleReplyAll"
          @forward="handleForward"
          @archive="handleArchive"
          @tag="handleTag"
          @distribute="handleDistribute"
          @translate="handleTranslate"
          @star="handleStar"
          @delete="handleDelete"
          @set-reminder="handleSetReminder"
          @navigate="handleNavigate"
          @fullscreen="handleFullscreen"
          @add-newclues="handleAddNewClues"
          @add-salesorder="handleAddSalesOrder"
          @edit-draft="handleEditDraft" />

        <div class="email-title-container">
          <div class="email-title-left">
            <div class="email-title-with-tags">
              <h2 class="email-title">{{ emailDetail.subject }}</h2>
              <!-- 邮件标签紧挨着主题显示 -->
              <div class="email-tags-inline"
                v-if="emailDetail.tagList && emailDetail.tagList.length > 0">
                <span v-for="(tag, tagIndex) in emailDetail.tagList || []" :key="tagIndex"
                  class="email-tag-label" :style="{ backgroundColor: tag.colorClass ? tag.colorClass : '#999' }">
                  {{ tag.tagName }}
                  <x-icon class="icon-tiny" @click="handleRemoveTag(tag.tagId)" />
                </span>
              </div>
            </div>

            <!-- 发送状态字段（仅发件箱邮件显示） -->
          </div>
          <div class="email-recipients-exchange-row">
            <!-- 发给字段 -->
            <div class="email-recipients-info"
              v-if="emailDetail && emailDetail.toList && emailDetail.toList.length > 0">
              <span class="recipients-label">发给：</span>
              <span class="recipients-value">{{ getRecipientUsernames(emailDetail.toList) }}</span>
            </div>

            <div class="email-title-right">
              <!-- 往来信息 -->
              <div class="email-exchange-info">
                <div class="exchange-time">{{ emailDetail.sentTime || emailDetail.receivedTime }}
                </div>
                <div>
                  <span class="exchange-count" @click="handleOpenEmailExchange">全部往来</span>
                </div>
                <button class="toggle-meta-btn" @click="toggleEmailMeta">
                  {{ showEmailMeta ? '隐藏' : '显示' }}
                </button>
              </div>
            </div>
          </div>
          <div class="email-send-status" v-if="emailDetail.status === 'sent'">
            <span class="send-status-label">发送状态：</span>
            <span class="send-status-value">
              {{ getSendStatusText(emailDetail.trackingStatus) }}
            </span>
          </div>
        </div>
      </div>

      <div class="email-meta-container" v-show="showEmailMeta">
        <!-- 发件人行 -->
        <div class="email-meta">
              <div class="sender-info">
                <div class="detail-label">发件人：</div>
                  <div class="recipients">
                    <div class="custom-tooltip-container"
                        style="position: relative; display: inline-block;"
                        @mouseenter="showSenderTooltipHandler($event, emailDetail.sendEmailAddress)"
                        @mouseleave="handleSenderLeave">

                      <span class="recipient copy-text user-display"
                            @click="handleCopyToClipboard(emailDetail.sendEmailAddress)">
                        <span class="user-name-trigger">
                          {{ extractUserName(emailDetail.sendEmailAddress) }}
                        </span>
                        &lt;{{ emailDetail.sendEmailAddress }}&gt;
                      </span>

                      <div v-show="showSenderTooltip"
                          class="custom-tooltip person-info-tooltip"
                          :style="{ left: tooltipPosition.x + 'px', top: tooltipPosition.y + 'px' }"
                          @mouseenter="handleTooltipMouseEnter"
                          @mouseleave="handleSenderLeave">
                        <customerCard :customerInfo="localCustomerInfo"
                          :selectedEmail="emailDetail.sendEmailAddress" :composeData="composeData"
                          @hide-tooltip="hideSenderTooltip" />
                      </div>
                    </div>
                  <span v-if="emailDetail.tag" class="email-tag-badge">@{{ emailDetail.tag }}</span>
                </div>
              </div>
              <div class="email-date">{{ emailDetail.sentTime }}</div>
            </div>

            <div class="email-meta">
              <div class="sender-info">
                <div class="detail-label">收件人：</div>
                <div class="recipients">
                  <div class="custom-tooltip-container"
                   v-for="(recipient, idx) in (showAllTo ? emailDetail.toList : emailDetail.toList.slice(0, 20))"
                   :key="idx"
                       style="position: relative; display: inline-block;"
                       @mouseenter="showToTooltipHandler($event, recipient.emailAddress, idx)"
                       @mouseleave="handleToLeave">
                    <span class="recipient copy-text user-display"
                      @click="handleCopyToClipboard(recipient.emailAddress)"
                      style="cursor: pointer;">
                      <span class="user-name-trigger">
                          {{ extractUserName(recipient.emailAddress) }}
                        </span>
                        &lt;{{ recipient.emailAddress }}&gt;
                    </span>
                    <div v-show="showToTooltip && activeTooltipIndex === idx && activeTooltipType === 'to'"
                         class="custom-tooltip person-info-tooltip"
                         :style="{ left: tooltipPosition.x + 'px', top: tooltipPosition.y + 'px' }"
                         @mouseenter="handleTooltipMouseEnter"
                         @mouseleave="handleToLeave">
                      <customerCard :customerInfo="localCustomerInfo" :selectedEmail="recipient.emailAddress" :composeData="composeData"
                        @hide-tooltip="hideToTooltip">
                      </customerCard>
                    </div>
                  </div>
                  <span v-if="emailDetail.toList && emailDetail.toList.length > 20"
                    class="more-recipients" @click="toggleToExpand">
                    {{ showAllTo ? '收起' : `等${emailDetail.toList.length - 20}人` }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 抄送人信息 -->
            <div class="email-meta" v-if="emailDetail.ccList && emailDetail.ccList.length > 0">
              <div class="sender-info">
                <div class="detail-label">抄送人：</div>
                <div class="recipients">
                  <div class="custom-tooltip-container"
                  v-for="(recipient, idx) in (showAllCc ? emailDetail.ccList : emailDetail.ccList.slice(0, 10))"
                  :key="idx"
                       style="position: relative; display: inline-block;"
                       @mouseenter="showCcTooltipHandler($event, recipient.emailAddress, idx)"
                       @mouseleave="handleCcLeave">
                    <span class="recipient copy-text user-display"
                      @click="handleCopyToClipboard(recipient.emailAddress)"
                      style="cursor: pointer;">
                      <span class="user-name-trigger">
                          {{ extractUserName(recipient.emailAddress) }}
                        </span>
                        &lt;{{ recipient.emailAddress }}&gt;
                    </span>
                    <div v-show="showCcTooltip && activeTooltipIndex === idx && activeTooltipType === 'cc'"
                         class="custom-tooltip person-info-tooltip"
                         :style="{ left: tooltipPosition.x + 'px', top: tooltipPosition.y + 'px' }"
                         @mouseenter="handleTooltipMouseEnter"
                         @mouseleave="handleCcLeave">
                      <customerCard :customerInfo="localCustomerInfo" :selectedEmail="recipient.emailAddress"
                        @hide-tooltip="hideCcTooltip">
                      </customerCard>
                    </div>
                  </div>
                  <span v-if="emailDetail.ccList.length > 10" class="more-recipients" @click="toggleCcExpand">
                    {{ showAllCc ? '收起' : `等${emailDetail.ccList.length - 10}人` }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 密送人信息 -->
            <div class="email-meta" v-if="emailDetail.bccList && emailDetail.bccList.length > 0">
              <div class="sender-info">
                <div class="detail-label">密送人：</div>
                <div class="recipients">
                 <span v-for="(recipient, idx) in emailDetail.bccList" :key="idx" class="recipient">
                    {{ recipient.emailAddress }}
                  </span>
                </div>
              </div>
            </div>

        <!-- 发送时间和接收时间行 -->
        <div class="email-details-container">
          <div class="detail-group">
            <div class="detail-item">
              <div class="detail-label">发送时间：</div>
              <div class="detail-value">{{ formatDateTime(emailDetail.sentTime) }}</div>
            </div>
            <div class="detail-item" v-if="emailDetail.receivedTime">
              <div class="detail-label">接收时间：</div>
              <div class="detail-value">{{ formatDateTime(emailDetail.receivedTime) || '-' }}</div>
            </div>
            <div class="detail-item" v-else>
              <div class="detail-label"></div>
              <div class="detail-value"></div>
            </div>
            <div class="detail-item">
              <div class="detail-label">优先级：</div>
              <div class="detail-value">{{ emailDetail.priority == 1 ? '普通' : '一对一' }}</div>
            </div>
          </div>
          <div class="detail-group">
            <div class="detail-item">
              <div class="detail-label">目录：</div>
              <div class="detail-value">{{ getActiveFolder(emailDetail.status) }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">归属账号：</div>
              <div class="detail-value">{{ emailDetail.belongMailAccountAddress }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">拥有人：</div>
              <div class="detail-value">{{ emailDetail.belongMailAccountUserName }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- AI智能摘要区域 -->
      <div class="ai-summary-section" v-if="showAiSummary">
        <div class="ai-summary-header">
          <div class="ai-summary-title">
            <zap-icon class="icon-small" /> AI智能摘要
          </div>
          <button class="close-summary-btn" @click="closeAiSummary">
            <x-icon class="icon-tiny" />
          </button>
        </div>
        <div class="ai-summary-content">
          <div v-if="isGeneratingSummary" class="summary-loading">
            <rotate-cw-icon class="icon-small spin" /> 正在生成摘要...
          </div>
          <div v-else class="summary-text">{{ aiSummaryContent }}</div>
        </div>
      </div>

      <!-- 语言选择面板 -->
      <div class="language-selector" v-if="emailDetail.showLanguageSelector">
        <div class="language-selector-inline">
          <div class="language-label">
            <languages-icon class="icon-small" />
          </div>
          <div class="language-selects">
            <select v-model="emailDetail.translationSourceLanguage" class="source-lang">
              <option value="auto">自动检测</option>
              <option v-for="lang in languages" :key="lang.code" :value="lang.code">{{ lang.name }}
              </option>
            </select>
            <div class="arrow-icon">→</div>
            <select v-model="emailDetail.translationLanguage" class="target-lang">
              <option v-for="lang in languages" :key="lang.code" :value="lang.code">{{ lang.name }}
              </option>
            </select>
          </div>
          <div class="language-actions">
            <button class="translate-btn" @click="handlePerformTranslation">
              <languages-icon class="icon-small" /> 翻译
            </button>
            <button class="close-selector-btn" @click="emailDetail.showLanguageSelector = false">
              <x-icon class="icon-tiny" /> 关闭翻译
            </button>
          </div>
        </div>
      </div>

      <!-- 附件显示区域 -->
      <div class="email-attachments" v-if="emailDetail.fileList && emailDetail.fileList.length > 0">
        <div class="attachments-header">
          <paperclip-icon class="icon-small" /> 附件 ({{ emailDetail.fileList.length }})
        </div>
        <div class="attachments-list">
          <div v-for="(attachment, index) in emailDetail.fileList" :key="index"
               class="attachment-item"
               :style="getAttachmentItemStyle(attachment.name)">
            <div class="attachment-icon">
              <file-text-icon v-if="isDocumentFile(attachment.name)" class="icon" />
              <image-icon v-else-if="isImageFile(attachment.name)" class="icon" />
              <file-icon v-else class="icon" />
            </div>
            <div class="attachment-info">
              <div class="attachment-name" :title="attachment.name">{{ attachment.name }}</div>
              <div class="attachment-size">{{ formatFileSize(attachment.size) }}</div>
            </div>
            <div class="attachment-actions">
              <eye-icon v-if="canPreviewFile(attachment.name)" class="icon-small" title="预览"
                @click.stop="handlePreviewAttachment(attachment)" />
              <download-icon class="icon-small" title="下载" @click.stop="handleDownloadAttachment(attachment)" />
            </div>
          </div>
        </div>
      </div>

      <!-- 邮件内容区域 -->
      <div class="email-content-wrapper">
        <!-- 原始邮件内容 -->
        <div v-show="!emailDetail.showTranslation" class="original-content">
          <div class="email-body" v-html="emailDetail.content"></div>
        </div>
        <!-- 翻译结果 -->
        <div class="translation-result" v-if="emailDetail.showTranslation">
          <div class="translation-header">
            <div>翻译结果 ({{ getLanguageName(emailDetail.translationLanguage) }})</div>
            <div class="translation-actions">
              <button class="view-original" @click="handleToggleOriginalView">
                {{ emailDetail.showOriginalContent ? '隐藏原文' : '显示原文' }}
              </button>
              <button class="close-translation" @click="handleCloseTranslation">
                <x-icon class="icon-tiny" /> 关闭
              </button>
            </div>
          </div>
          <div class="translation-content">
            <div v-if="emailDetail.isTranslating" class="translation-loading">
              <rotate-cw-icon class="icon-small spin" /> 正在翻译...
            </div>
            <div v-else class="translated-text" v-html="emailDetail.translatedContent"></div>
          </div>

          <!-- 原文内容（可切换显示） -->
          <div v-if="emailDetail.showOriginalContent" class="original-content-preview">
            <div class="original-content-header">原文内容</div>
            <div class="email-body" v-html="emailDetail.content"></div>
            <div class="email-signature" v-if="emailDetail.signature">
              <div class="signature-header">签名</div>
              <div class="signature-content" v-html="emailDetail.signature"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCustomerByEmailAddress } from '@/api/crm/email'
import EmailToolbar from './EmailToolbar.vue'
import CustomerCard from './customerCard.vue'
import {
  X, Zap, RotateCw, Languages, Paperclip,
  FileText, Image, File, Eye, Download
} from 'lucide-vue'
import { formatFileSize, isDocumentFile, isImageFile, canPreviewFile, formatDateTime } from '@/utils/format'

export default {
  name: 'EmailDetailContent',
  components: {
    EmailToolbar,
    CustomerCard,
    XIcon: X,
    ZapIcon: Zap,
    RotateCwIcon: RotateCw,
    LanguagesIcon: Languages,
    PaperclipIcon: Paperclip,
    FileTextIcon: FileText,
    ImageIcon: Image,
    FileIcon: File,
    EyeIcon: Eye,
    DownloadIcon: Download
  },
  props: {
    emailDetail: {
      type: Object,
      default: null
    },
    detailLoading: {
      type: Boolean,
      default: false
    },
    showToolbar: {
      type: Boolean,
      default: true
    },
    hasPrevEmail: {
      type: Boolean,
      default: false
    },
    hasNextEmail: {
      type: Boolean,
      default: false
    },
    composeData: {
      type: Object,
      default: () => ({})
    },
    languages: {
      type: Array,
      default: () => []
    },
    // 传递状态数据
    showEmailMeta: {
      type: Boolean,
      default: false
    },
    showAiSummary: {
      type: Boolean,
      default: false
    },
    isGeneratingSummary: {
      type: Boolean,
      default: false
    },
    aiSummaryContent: {
      type: String,
      default: ''
    },

    // 展开状态
    showAllTo: {
      type: Boolean,
      default: false
    },
    showAllCc: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tooltipTimer1: null,
      tooltipTimer2: null,
      tooltipTimer3: null,
      showSenderTooltip: false,
      showToTooltip:false,
      showCcTooltip:false,
      activeTooltipIndex: -1, // 当前激活的收件人索引
      activeTooltipType: '', // 当前激活的tooltip类型: 'to' 或 'cc'
      tooltipPosition: { x: 0, y: 0 },
      // 本地状态数据
      localCustomerInfo: {} // 本地客户信息，避免直接修改prop
    }
  },
  methods: {
    // 工具函数
    formatFileSize,
    isDocumentFile,
    isImageFile,
    canPreviewFile,
    formatDateTime,

    // 工具栏事件处理
    handleReply(data) {
      this.$emit('reply', data)
    },
    handleReplyAll(data) {
      this.$emit('reply-all', data)
    },
    handleForward(data) {
      this.$emit('forward', data)
    },
    handleArchive() {
      this.$emit('archive', this.emailDetail)
    },
    handleTag() {
      this.$emit('tag', this.emailDetail)
    },
    handleDistribute() {
      this.$emit('distribute', this.emailDetail)
    },
    handleTranslate() {
      this.$emit('translate', this.emailDetail)
    },
    handleStar() {
      this.$emit('star', this.emailDetail)
    },
    handleDelete() {
      this.$emit('delete', this.emailDetail)
    },
    handleSetReminder(data) {
      this.$emit('set-reminder', data)
    },
    handleNavigate(direction) {
      this.$emit('navigate', direction)
    },
    handleFullscreen() {
      this.$emit('fullscreen', this.emailDetail)
    },
    handleAddNewClues() {
      this.$emit('add-newclues', this.emailDetail)
    },
    handleAddSalesOrder() {
      this.$emit('add-salesorder', this.emailDetail)
    },
    handleEditDraft() {
      this.$emit('edit-draft', this.emailDetail)
    },

    // 邮件内容相关事件
    toggleEmailMeta() {
      this.$emit('toggle-email-meta')
    },
    handleOpenEmailExchange() {
      this.$emit('open-email-exchange', this.emailDetail)
    },
    handleRemoveTag(tagId) {
      this.$emit('remove-tag', { email: this.emailDetail, tagId })
    },

    // 复制到剪贴板
    handleCopyToClipboard(text) {
      if (!text) return;

      navigator.clipboard.writeText(text).then(() => {
        this.$message.success('复制成功');
      }).catch(err => {
        this.$message.error('复制失败');
        console.error('复制失败：', err);
      });
    },
    // 显示发件人tooltip
    showSenderTooltipHandler(event, email) {
      // 清除所有tooltip定时器
      clearTimeout(this.tooltipTimer1)
      clearTimeout(this.tooltipTimer2)
      clearTimeout(this.tooltipTimer3)

      // 隐藏其他tooltip
      this.showToTooltip = false
      this.showCcTooltip = false

      this.handleHover(email);
      this.setTooltipPosition(event);
      this.activeTooltipIndex = -1;
      this.activeTooltipType = 'sender';
      this.showSenderTooltip = true;
    },

    showToTooltipHandler(event, email, index) {
      // 清除所有tooltip定时器
      clearTimeout(this.tooltipTimer1)
      clearTimeout(this.tooltipTimer2)
      clearTimeout(this.tooltipTimer3)

      // 隐藏其他tooltip
      this.showSenderTooltip = false
      this.showCcTooltip = false

      this.handleHover(email);
      this.setTooltipPosition(event);
      this.activeTooltipIndex = index;
      this.activeTooltipType = 'to';
      this.showToTooltip = true;
    },

    showCcTooltipHandler(event, email, index) {
      // 清除所有tooltip定时器
      clearTimeout(this.tooltipTimer1)
      clearTimeout(this.tooltipTimer2)
      clearTimeout(this.tooltipTimer3)

      // 隐藏其他tooltip
      this.showSenderTooltip = false
      this.showToTooltip = false

      this.handleHover(email);
      this.setTooltipPosition(event);
      this.activeTooltipIndex = index;
      this.activeTooltipType = 'cc';
      this.showCcTooltip = true;
    },

    // 设置tooltip位置
    setTooltipPosition(event) {
      const rect = event.target.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

      // 计算tooltip的位置
      let left = rect.left + scrollLeft;
      let top = rect.bottom + scrollTop + 5;

      // 获取视窗尺寸
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // 预估tooltip宽度（可以根据实际情况调整）
      const tooltipWidth = 250;
      const tooltipHeight = 200;

      // 水平位置调整：如果超出右边界，向左调整
      if (left + tooltipWidth > viewportWidth) {
        left = viewportWidth - tooltipWidth - 10;
      }

      // 垂直位置调整：如果超出下边界，显示在元素上方
      if (top + tooltipHeight > viewportHeight + scrollTop) {
        top = rect.top + scrollTop - tooltipHeight - 5;
      }

      // 确保不超出左边界
      if (left < 10) {
        left = 10;
      }

      // 确保不超出上边界
      if (top < scrollTop + 10) {
        top = scrollTop + 10;
      }

      this.tooltipPosition = {
        x: left,
        y: top
      };
    },

    // 鼠标进入tooltip时清除关闭定时器
    handleTooltipMouseEnter() {
      clearTimeout(this.tooltipTimer1);
      clearTimeout(this.tooltipTimer2);
      clearTimeout(this.tooltipTimer3);
    },

    handleSenderLeave(){
      this.tooltipTimer1 = setTimeout(() => {
        this.showSenderTooltip = false
        this.activeTooltipIndex = -1
        this.activeTooltipType = ''
      }, 200)
    },

    handleToLeave(){
      this.tooltipTimer2 = setTimeout(() => {
        this.showToTooltip = false
        this.activeTooltipIndex = -1
        this.activeTooltipType = ''
      }, 200)
    },

    handleCcLeave(){
      this.tooltipTimer3 = setTimeout(() => {
        this.showCcTooltip = false
        this.activeTooltipIndex = -1
        this.activeTooltipType = ''
      }, 200)
    },

    // 隐藏发件人tooltip
    hideSenderTooltip() {
      clearTimeout(this.tooltipTimer1);
      this.showSenderTooltip = false
      this.activeTooltipIndex = -1
      this.activeTooltipType = ''
    },

    // 隐藏收件人tooltip
    hideToTooltip() {
      clearTimeout(this.tooltipTimer2);
      this.showToTooltip = false
      this.activeTooltipIndex = -1
      this.activeTooltipType = ''
    },

    // 隐藏抄送人tooltip
    hideCcTooltip() {
      clearTimeout(this.tooltipTimer3);
      this.showCcTooltip = false
      this.activeTooltipIndex = -1
      this.activeTooltipType = ''
    },

    // 隐藏所有tooltip
    hideAllTooltips() {
      clearTimeout(this.tooltipTimer1);
      clearTimeout(this.tooltipTimer2);
      clearTimeout(this.tooltipTimer3);
      this.showSenderTooltip = false
      this.showToTooltip = false
      this.showCcTooltip = false
      this.activeTooltipIndex = -1
      this.activeTooltipType = ''
    },

    handleHover(value) {
      if (!value) return;
      if(value == this.composeData.from){
        this.localCustomerInfo = {};
      }else{
        getCustomerByEmailAddress(value)
        .then(res => {
          if (res.data) {
            this.localCustomerInfo = res.data;
          } else {
            this.localCustomerInfo = {};
          }
        })
        .catch(() => {
          this.localCustomerInfo = {};
        })
      }
    },

    // 提取邮箱地址中@前的用户名
    extractUserName(email) {
      if (!email) return '';
      const atIndex = email.indexOf('@');
      return atIndex > 0 ? email.substring(0, atIndex) : email;
    },


    // 展开/收起相关
    toggleToExpand() {
      this.$emit('toggle-to-expand')
    },
    toggleCcExpand() {
      this.$emit('toggle-cc-expand')
    },

    // AI摘要相关
    closeAiSummary() {
      this.$emit('close-ai-summary')
    },

    // 翻译相关
    handlePerformTranslation() {
      this.$emit('perform-translation')
    },
    handleToggleOriginalView() {
      this.$emit('toggle-original-view')
    },
    handleCloseTranslation() {
      this.$emit('close-translation')
    },

    // 附件相关
    handlePreviewAttachment(attachment) {
      this.$emit('preview-attachment', attachment)
    },
    handleDownloadAttachment(attachment) {
      this.$emit('download-attachment', attachment)
    },

    // 辅助方法
    getRecipientUsernames(toList) {
      if (!toList || toList.length === 0) return '';

      const maxDisplay = 5;
      const usernames = toList.map(recipient => {
        const email = recipient.emailAddress || recipient;
        return email.split('@')[0];
      });

      if (usernames.length <= maxDisplay) {
        return usernames.join('，');
      } else {
        const displayNames = usernames.slice(0, maxDisplay).join('，');
        const remainingCount = usernames.length - maxDisplay;
        return `${displayNames}，等${remainingCount}人`;
      }
    },
    getSendStatusText(trackingStatus) {
      // 根据追踪状态返回对应的文本
      switch (trackingStatus) {
        case 'sent': return '已发送'
        case 'delivered': return '已送达'
        case 'failed': return '发送失败'
        default: return '已发送'
      }
    },

    getActiveFolder(status) {
      switch (status) {
        case 'inbox': return '收件箱'
        case 'sent': return '发件箱'
        case 'draft': return '草稿箱'
        case 'trash': return '垃圾箱'
        default: return '收件箱'
      }
    },
    getLanguageName(code) {
      if (!this.languages || !Array.isArray(this.languages)) return code
      const lang = this.languages.find(l => l.code === code)
      return lang ? lang.name : code
    },

    // 根据文件名长度计算附件项样式
    getAttachmentItemStyle(fileName) {
      if (!fileName) return {}

      // 基础宽度
      const baseWidth = 200
      // 每个字符增加的宽度（中文字符按2个字符计算）
      const charWidth = 8

      // 计算文件名的实际字符长度（中文字符按2个字符计算）
      let charCount = 0
      for (let i = 0; i < fileName.length; i++) {
        const char = fileName.charAt(i)
        // 判断是否为中文字符
        if (/[\u4e00-\u9fa5]/.test(char)) {
          charCount += 2
        } else {
          charCount += 1
        }
      }

      // 计算宽度
      const calculatedWidth = baseWidth + (charCount * charWidth)

      // 限制最小和最大宽度
      const minWidth = 200
      const maxWidth = 450
      const finalWidth = Math.max(minWidth, Math.min(maxWidth, calculatedWidth))

      return {
        width: `${finalWidth}px`,
        flexShrink: 0
      }
    }
  }
}
</script>

<style scoped>
/* 邮件详情内容容器 */
.email-detail-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 加载动画 */
.email-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #666;
}

/* 邮件头部 - 参考截图优化 */
.email-header {
  border-bottom: 1px solid #e8e8e8;
  background: white;
  position: sticky;
  top: 0;
  z-index: 5;
}

.email-title-container {
  padding: 12px 16px;
}

.email-title-left {
  margin-bottom: 8px;
}

.email-title-with-tags {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 6px;
  line-height: 1.3;
}

.email-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0;
  line-height: 1.3;
  flex-shrink: 0;
  word-break: break-word;
}

.email-tags-inline {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 6px;
  margin-top: 1px; /* 微调对齐 */
}

.email-tag-label {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 12px;
  color: white;
  font-weight: 500;
  height: 20px; /* 固定高度确保对齐 */
  box-sizing: border-box;
  white-space: nowrap;
}

.email-tag-label .icon-tiny {
  width: 12px;
  height: 12px;
  cursor: pointer;
  opacity: 0.8;
}

.email-tag-label .icon-tiny:hover {
  opacity: 1;
}

.email-recipients-exchange-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 4px;
}

.email-recipients-info {
  display: flex;
  align-items: flex-start;
  gap: 4px;
  font-size: 13px;
  flex: 1;
}

.recipients-label {
  color: #8c8c8c;
  font-weight: 400;
  white-space: nowrap;
  margin-top: 1px;
}

.recipients-value {
  color: #262626;
  line-height: 1.4;
  word-break: break-all;
}

.email-title-right {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  flex-shrink: 0;
}

.email-exchange-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
}

.exchange-time {
  color: #8c8c8c;
  white-space: nowrap;
}

.exchange-count {
  color: #1890ff;
  cursor: pointer;
  text-decoration: none;
  white-space: nowrap;
}

.exchange-count:hover {
  text-decoration: underline;
}

.toggle-meta-btn {
  background: #fff;
  border: 1px solid #d9d9d9;
  color: #595959;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.toggle-meta-btn:hover {
  border-color: #40a9ff;
  color: #1890ff;
}

.email-send-status {
  margin-top: 8px;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.send-status-label {
  color: #909399;
  font-weight: 500;
}

.send-status-value {
  color: #67c23a;
  font-weight: 500;
}

/* Meta信息容器 - 严格按照截图样式 */
.email-meta-container {
  border: 1px solid #e8e8e8;
  background: #fafafa;
  margin: 8px 16px 12px 16px;
  font-size: 13px;
  line-height: 1.4;
}

.email-meta {
  padding: 6px 16px;
  display: flex;
  justify-content: space-between;
}

.sender-info {
  display: flex;
}

.recipients {
  display: flex;
  flex-wrap: wrap;
  margin-left: 8px;
}

.recipient {
  margin-right: 8px;
}

.more-recipients {
  color: #409eff;
  cursor: pointer;
  font-size: 14px;
  margin-left: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: #ecf5ff;
  border: 1px solid #b3d8ff;
  transition: all 0.3s;
}

.more-recipients:hover {
  background-color: #409eff;
  color: white;
}

.email-date {
  color: #999;
}

/* 用户显示样式 */
.user-display {
  font-weight: 500;
  color: #333;
}

.user-name-trigger {
  text-decoration: underline;
  cursor: pointer;
}

.email-tag-badge {
  background-color: #f0f0f0;
  color: #666;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  margin-left: 8px;
}

/* Meta信息行样式 */
.email-meta-row {
  padding: 6px 12px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: flex-start;
  gap: 4px;
}

.email-meta-row:last-child {
  border-bottom: none;
}

/* 标签样式 */
.meta-label {
  color: #666;
  font-weight: 400;
  min-width: 60px;
  flex-shrink: 0;
  white-space: nowrap;
}

.meta-label-right {
  color: #666;
  font-weight: 400;
  min-width: 60px;
  flex-shrink: 0;
  white-space: nowrap;
  margin-left: 20px;
}

/* 内容样式 */
.meta-content {
  color: #333;
  flex: 1;
  word-break: break-all;
  line-height: 1.4;
}

/* 时间样式 */
.meta-time {
  color: #666;
  white-space: nowrap;
  margin-left: auto;
}

/* 发件人和收件人名称 */
.sender-name,
.recipient-name {
  color: #333;
  font-weight: 400;
}

/* 邮箱地址样式 */
.sender-email,
.recipient-email {
  color: #333;
  cursor: pointer;
  text-decoration: none;
}

.sender-email:hover,
.recipient-email:hover {
  text-decoration: underline;
}

/* 收件人项目 */
.recipient-item {
  display: inline;
}

/* 更多收件人链接 */
.more-recipients {
  color: #1890ff;
  cursor: pointer;
  font-size: 13px;
  text-decoration: none;
  margin-left: 4px;
}

.more-recipients:hover {
  text-decoration: underline;
}

/* 工具提示 */
.custom-tooltip-container {
  position: relative;
  display: inline-block;
}

.custom-tooltip {
  position: fixed;
  z-index: 9999;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
  padding: 12px;
  max-width: 300px;
  animation: tooltipFadeIn 0.2s ease-out;
  pointer-events: auto;
}

.person-info-tooltip {
  min-width: 250px;
  max-width: 350px;
}

/* Tooltip 动画 */
@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}



/* AI智能摘要 */
.ai-summary-section {
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #f8f9fa;
}

.ai-summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.ai-summary-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.close-summary-btn {
  background: none;
  border: none;
  color: #909399;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-summary-btn:hover {
  background: #f0f0f0;
  color: #606266;
}

.ai-summary-content {
  font-size: 14px;
  line-height: 1.6;
}

.summary-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
}

.summary-text {
  color: #606266;
}

/* 语言选择器 */
.language-selector {
  padding: 12px 20px;
  background: #f0f9ff;
  border-bottom: 1px solid #e8e8e8;
}

.language-selector-inline {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.language-label {
  display: flex;
  align-items: center;
  color: #409eff;
  font-weight: 500;
}

.language-selects {
  display: flex;
  align-items: center;
  gap: 8px;
}

.source-lang,
.target-lang {
  padding: 4px 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 13px;
  background: white;
}

.arrow-icon {
  color: #909399;
  font-weight: bold;
}

.language-actions {
  display: flex;
  gap: 8px;
}

.translate-btn,
.close-selector-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid #409eff;
  background: #409eff;
  color: white;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.close-selector-btn {
  background: #f56c6c;
  border-color: #f56c6c;
}

.translate-btn:hover {
  background: #66b1ff;
  border-color: #66b1ff;
}

.close-selector-btn:hover {
  background: #f78989;
  border-color: #f78989;
}

.email-details-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  font-size: 14px;
  color: #606266;
  padding: 0 16px;
}

.detail-group {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.detail-item {
  width: 48%;
  display: flex;
  align-items: flex-start;
}

.detail-label {
  min-width: 60px;
  color: #909399;
  font-weight: 500;
}

.detail-value {
  flex: 1;
  word-break: break-word;
}


/* 附件区域 */
.email-attachments {
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: white;
}

.attachments-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.attachments-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: flex-start;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fafafa;
  transition: all 0.2s;
  min-width: 200px; /* 最小宽度 */
  max-width: 400px; /* 最大宽度 */
  flex: 0 1 auto; /* 允许收缩和扩展 */
}

.attachment-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.attachment-icon {
  color: #909399;
  flex-shrink: 0;
}

.attachment-info {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.attachment-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  word-break: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.attachment-size {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.attachment-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.attachment-actions .icon-small {
  width: 16px;
  height: 16px;
  color: #409eff;
  cursor: pointer;
  transition: color 0.2s;
}

.attachment-actions .icon-small:hover {
  color: #66b1ff;
}

/* 邮件内容区域 */
.email-content-wrapper {
  flex: 1;
  padding: 20px;
  background: white;
  overflow-y: auto;
}

.original-content,
.translation-result {
  line-height: 1.6;
}

.email-body {
  font-size: 14px;
  color: #303133;
  word-wrap: break-word;
}

/* 翻译结果 */
.translation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
}

.translation-actions {
  display: flex;
  gap: 8px;
}

.view-original,
.close-translation {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: 1px solid #dcdfe6;
  background: white;
  color: #606266;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.view-original:hover,
.close-translation:hover {
  border-color: #409eff;
  color: #409eff;
}

.translation-content {
  margin-bottom: 16px;
}

.translation-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
  font-size: 14px;
}

.translated-text {
  font-size: 14px;
  color: #303133;
  line-height: 1.6;
}

.original-content-preview {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
}

.original-content-header {
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  font-size: 14px;
}

.email-signature {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.signature-header {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
  font-weight: 500;
}

.signature-content {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

/* 图标样式 */
.icon-tiny {
  width: 14px;
  height: 14px;
}

.icon-small {
  width: 16px;
  height: 16px;
}

.icon {
  width: 20px;
  height: 20px;
}

/* 动画 */
.spin {
  animation: spin 1s linear infinite;
}

/* 复制文本样式 */
.copy-text {
  cursor: pointer;
  transition: color 0.2s;
}

.copy-text:hover {
  color: #409eff;
}

/* 大屏幕优化 - 显示三列附件 */
@media (min-width: 1200px) {
  .attachments-list {
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .email-title-with-tags {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .email-tags-inline {
    margin-top: 0;
  }

  .email-recipients-exchange-row {
    flex-direction: column;
    gap: 8px;
  }

  .email-details-container {
    flex-direction: column;
    gap: 20px;
  }

  .language-selector-inline {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .translation-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  /* 中等屏幕附件优化 */
  .attachment-item {
    min-width: 180px;
    max-width: 350px;
  }
}

/* 小屏幕优化 */
@media (max-width: 480px) {
  .email-title-with-tags {
    gap: 6px;
  }

  .email-title {
    font-size: 15px;
  }

  .email-tag-label {
    font-size: 11px;
    padding: 2px 6px;
    height: 18px;
  }

  /* 小屏幕附件优化 */
  .attachments-list {
    flex-direction: column;
    gap: 8px;
  }

  .attachment-item {
    padding: 6px 10px;
    gap: 10px;
    width: 100% !important;
    min-width: auto !important;
    max-width: none !important;
  }

  .attachment-name {
    font-size: 13px;
    white-space: normal !important;
    text-overflow: unset !important;
    overflow: visible !important;
    word-break: break-word;
  }

  .attachment-size {
    font-size: 11px;
  }
}
</style>