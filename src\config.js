const getLocationOrigin = () => {
  return window.location.protocol + '//' + window.location.hostname + (window.location.port ? ':' + window.location.port : '')
}
const macUrl = '#'
const winUrl = '#'
const iOSUrl = '#'
const androidUrl = '#'

const registerUrl = '#'
const loginUrl = '#'
const contactUrl = '#' // 联系我们

const companyName = '奔达 - CRM'
const version = 'V12.0.0'
const baiduKey = 'LEcDcElRR6zFXoaG6jtANQYW'

const build = 20220110

// 默认表格样式
const tableStyle = {
  stripe: true, // 斑马纹
  class: [] // 'is-right-border-style', 'is-bottom-border-style'
}

const languages = [
  { code: 'zh', name: '中文' },
  { code: 'en', name: '英文' },
  { code: 'ja', name: '日语' },
  { code: 'ko', name: '韩语' },
  { code: 'fr', name: '法语' },
  { code: 'de', name: '德语' },
  { code: 'es', name: '西班牙语' },
  { code: 'ru', name: '俄语' },
   { code: 'pt', name: '葡萄牙语' },       // 巴西、葡萄牙
  { code: 'hi', name: '印地语' },          // 印度广泛使用
  { code: 'ar', name: '阿拉伯语' },        // 中东、北非地区广泛使用
  { code: 'it', name: '意大利语' },        // 欧洲常用
  { code: 'vi', name: '越南语' },          // 东南亚
  { code: 'th', name: '泰语' },            // 东南亚
  { code: 'id', name: '印尼语' },          // 东南亚人口大国
  { code: 'tr', name: '土耳其语' },        // 欧洲和西亚交汇地带
  { code: 'pl', name: '波兰语' },          // 东欧
  { code: 'uk', name: '乌克兰语' },        // 东欧
  { code: 'ms', name: '马来语' },          // 马来西亚、新加坡部分人群使用
  { code: 'fa', name: '波斯语' },          // 伊朗等国
  { code: 'he', name: '希伯来语' }         // 以色列官方语言
]

const countries = [
  "中国", "美国", "英国", "法国", "德国", "日本", "韩国", "印度", "俄罗斯", "加拿大",
    "澳大利亚", "意大利", "西班牙", "巴西", "新加坡", "马来西亚", "泰国", "菲律宾", "越南", "印度尼西亚",
    "阿联酋", "沙特阿拉伯", "土耳其", "南非", "埃及", "墨西哥", "阿根廷", "新西兰", "乌克兰", "波兰",
    "荷兰", "比利时", "瑞士", "瑞典", "挪威", "丹麦", "芬兰", "葡萄牙", "希腊", "以色列",
    "伊朗", "伊拉克", "卡塔尔", "巴基斯坦", "孟加拉国", "尼日利亚", "肯尼亚", "哈萨克斯坦", "阿尔及利亚", "摩洛哥",
    "智利", "哥伦比亚", "秘鲁", "委内瑞拉", "古巴", "捷克", "斯洛伐克", "匈牙利", "罗马尼亚", "保加利亚",
    "奥地利", "爱尔兰", "冰岛", "克罗地亚", "斯洛文尼亚", "塞尔维亚", "拉脱维亚", "立陶宛", "爱沙尼亚", "白俄罗斯",
    "格鲁吉亚", "亚美尼亚", "阿塞拜疆", "叙利亚", "黎巴嫩", "约旦", "科威特", "阿曼", "也门", "巴林",
    "斯里兰卡", "尼泊尔", "缅甸", "老挝", "柬埔寨", "文莱", "蒙古", "乌兹别克斯坦", "土库曼斯坦", "塔吉克斯坦",
    "阿富汗", "苏丹", "南苏丹", "埃塞俄比亚", "加纳", "坦桑尼亚", "赞比亚", "津巴布韦", "突尼斯", "利比亚",
    "安哥拉", "喀麦隆", "刚果（布）", "刚果（金）", "卢旺达", "乌干达", "塞内加尔", "马里", "布基纳法索", "马达加斯加",
    "莫桑比克", "博茨瓦纳", "纳米比亚", "毛里塔尼亚", "赤道几内亚", "加蓬", "贝宁", "多哥", "几内亚", "几内亚比绍",
    "塞拉利昂", "利比里亚", "冈比亚", "乍得", "中非", "尼日尔", "索马里", "吉布提", "厄立特里亚", "马拉维",
    "莱索托", "斯威士兰", "佛得角", "科摩罗", "毛里求斯", "塞舌尔", "汤加", "萨摩亚", "斐济", "所罗门群岛",
    "巴布亚新几内亚", "密克罗尼西亚", "帕劳", "马绍尔群岛", "基里巴斯", "瑙鲁", "图瓦卢", "东帝汶", "不丹", "马尔代夫",
    "卢森堡", "列支敦士登", "圣马力诺", "安道尔", "摩纳哥", "梵蒂冈", "巴哈马", "牙买加", "海地", "多米尼加",
    "圣卢西亚", "圣文森特和格林纳丁斯", "格林纳达", "安提瓜和巴布达", "圣基茨和尼维斯", "巴巴多斯", "特立尼达和多巴哥", "哥斯达黎加", "巴拿马", "洪都拉斯",
    "危地马拉", "尼加拉瓜", "萨尔瓦多", "冰岛", "爱沙尼亚", "立陶宛", "拉脱维亚", "马其顿", "黑山", "波斯尼亚和黑塞哥维那",
    "阿尔巴尼亚", "摩尔多瓦", "北马其顿", "梵蒂冈", "圣多美和普林西比", "东加", "瓦努阿图", "纽埃", "皮特凯恩群岛", "圣赫勒拿",
    "法属圭亚那", "马提尼克", "瓜德罗普", "留尼汪", "马约特", "法属波利尼西亚", "新喀里多尼亚", "瓦利斯和富图纳", "阿鲁巴", "库拉索",
    "波多黎各", "关岛", "美属萨摩亚", "美属维尔京群岛", "英属维尔京群岛", "开曼群岛", "百慕大", "直布罗陀", "福克兰群岛", "马恩岛",
    "泽西岛", "根西岛"
]

export default {
  version,
  build,
  companyName,
  getLocationOrigin,
  baiduKey,
  macUrl,
  winUrl,
  iOSUrl,
  androidUrl,
  registerUrl,
  tableStyle,
  loginUrl,
  contactUrl,
  languages,
  countries
}
